import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from "./cors.ts";
import nodemailer from "npm:nodemailer@6.9.10";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

const transport = nodemailer.createTransporter({
  host: Deno.env.get("SMTP_HOST"),
  port: Number(Deno.env.get("SMTP_PORT")),
  secure: true, // Use SSL
  auth: {
    user: Deno.env.get("SMTP_USER"),
    pass: Deno.env.get("SMTP_PASS"),
  },
});

Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method === "POST") {
    try {
      const requestData = await req.json();
      console.log('Request received:', requestData);

      // Extract data from request parameters
      const { party_id } = requestData;

      if (!party_id) {
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Missing required field: party_id"
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Call RPC function to check party and get user details
      const { data: rpcResult, error: rpcError } = await supabase
        .rpc('check_party_for_email', { input_party_id: party_id });

      if (rpcError) {
        console.error('RPC Error:', rpcError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Database error",
            error: rpcError.message
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      if (!rpcResult.success) {
        console.log('RPC returned error:', rpcResult.error);
        return new Response(
          JSON.stringify({
            status: "error",
            message: rpcResult.error,
            party_type: rpcResult.party_type || null
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Prepare email data
      const userName = rpcResult.user_name;
      const recipientEmail = rpcResult.user_email;
      const partyType = rpcResult.party_type;

      // Simple email content for party type notification
      const emailContent = `
<p>Dear ${userName},</p>

<p>This is a notification for your account.</p>

<p>Your party type: ${partyType}</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
      `;

      // Send email
      await new Promise<void>((resolve, reject) => {
        transport.sendMail({
          from: Deno.env.get("SMTP_USER"),
          to: recipientEmail,
          subject: "Account Notification - Competitor LDC Coaching Centre",
          html: emailContent,
        }, (error: any) => {
          if (error) {
            return reject(error);
          }
          resolve();
        });
      });

      console.log(`Email sent successfully to ${recipientEmail} for party type: ${partyType}`);

      return new Response(
        JSON.stringify({
          status: "success",
          message: "Email sent successfully",
          details: {
            recipient: recipientEmail,
            userName: userName,
            partyType: partyType,
            caregiver_party_id: rpcResult.caregiver_party_id
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );

    } catch (error) {
      console.error("Error processing request:", error);
      return new Response(
        JSON.stringify({
          status: "error",
          message: "Internal server error",
          error: error.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
  } else {
    return new Response("Method not allowed", { status: 405 });
  }
});
