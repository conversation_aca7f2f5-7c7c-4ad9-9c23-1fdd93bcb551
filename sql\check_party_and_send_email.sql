-- RPC function to check party type and return user details for email
CREATE OR <PERSON><PERSON>LACE FUNCTION recall_loop.check_party_for_email(input_party_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
    caregiver_party_id UUID;
    party_type_key_val TEXT;
    user_email TEXT;
    user_first_name TEXT;
    user_last_name TEXT;
    user_id_val UUID;
BEGIN
    -- Step 1: Get caregiver_party_id from game_assignment
    SELECT ga.caregiver_party_id 
    INTO caregiver_party_id
    FROM recall_loop.game_assignment ga
    WHERE ga.id = input_party_id;
    
    IF caregiver_party_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Game assignment not found'
        );
    END IF;
    
    -- Step 2: Get party_type_key
    SELECT pt.party_type_key 
    INTO party_type_key_val
    FROM recall_loop.party p
    JOIN recall_loop.party_type pt ON p.party_type_id = pt.id
    WHERE p.id = caregiver_party_id;
    
    IF party_type_key_val IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Party or party type not found'
        );
    END IF;
    
    -- Step 3: Check if party_type_key is allowed
    IF party_type_key_val NOT IN ('caregiver', 'tenant_admin') THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Party type not eligible for email',
            'party_type', party_type_key_val
        );
    END IF;
    
    -- Step 4: Get user details
    SELECT ua.user_id, p.first_name, p.last_name
    INTO user_id_val, user_first_name, user_last_name
    FROM recall_loop.user_account ua
    JOIN recall_loop.person p ON p.party_id = ua.party_id
    WHERE ua.party_id = caregiver_party_id;
    
    IF user_id_val IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User account or person not found'
        );
    END IF;
    
    -- Step 5: Get email from auth.users
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = user_id_val;
    
    IF user_email IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User email not found in auth'
        );
    END IF;
    
    -- Return success with all details
    RETURN json_build_object(
        'success', true,
        'caregiver_party_id', caregiver_party_id,
        'party_type', party_type_key_val,
        'user_email', user_email,
        'user_name', CONCAT(user_first_name, ' ', user_last_name),
        'first_name', user_first_name,
        'last_name', user_last_name
    );
    
EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
