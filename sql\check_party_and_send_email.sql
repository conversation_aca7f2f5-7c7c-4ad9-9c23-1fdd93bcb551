-- Simple function to check if party_id belongs to caregiver or tenant_admin
-- Returns 'yes' if eligible for email, 'no' if not, along with user details if yes
CREATE OR REPLACE FUNCTION recall_loop.check_party_eligibility(input_party_id UUID)
RETURNS JSON AS $$
DECLARE
    caregiver_party_id UUID;
    party_type_key_val TEXT;
    user_email TEXT;
    user_first_name TEXT;
    user_last_name TEXT;
    user_id_val UUID;
BEGIN
    -- Step 1: Get caregiver_party_id from game_assignment where id = party_id
    SELECT ga.caregiver_party_id
    INTO caregiver_party_id
    FROM recall_loop.game_assignment ga
    WHERE ga.id = input_party_id;

    IF caregiver_party_id IS NULL THEN
        RETURN json_build_object(
            'eligible', 'no',
            'reason', 'Game assignment not found'
        );
    END IF;

    -- Step 2: Get party_type_id from party where id = caregiver_party_id
    -- Step 3: Get party_type_key from party_type where party_type_id = (the key above)
    SELECT pt.party_type_key
    INTO party_type_key_val
    FROM recall_loop.party p
    JOIN recall_loop.party_type pt ON p.party_type_id = pt.id
    WHERE p.id = caregiver_party_id;

    IF party_type_key_val IS NULL THEN
        RETURN json_build_object(
            'eligible', 'no',
            'reason', 'Party or party type not found'
        );
    END IF;

    -- Step 4: Check whether party_type_key in ('caregiver', 'tenant_admin')
    IF party_type_key_val NOT IN ('caregiver', 'tenant_admin') THEN
        RETURN json_build_object(
            'eligible', 'no',
            'reason', 'Party type not eligible',
            'party_type', party_type_key_val
        );
    END IF;

    -- Step 5: Get user details for email sending
    SELECT ua.user_id, p.first_name, p.last_name
    INTO user_id_val, user_first_name, user_last_name
    FROM recall_loop.user_account ua
    JOIN recall_loop.person p ON p.party_id = ua.party_id
    WHERE ua.party_id = caregiver_party_id;

    IF user_id_val IS NULL THEN
        RETURN json_build_object(
            'eligible', 'no',
            'reason', 'User account or person not found'
        );
    END IF;

    -- Step 6: Get email from auth.users
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = user_id_val;

    IF user_email IS NULL THEN
        RETURN json_build_object(
            'eligible', 'no',
            'reason', 'User email not found in auth'
        );
    END IF;

    -- Return YES with all user details for email sending
    RETURN json_build_object(
        'eligible', 'yes',
        'caregiver_party_id', caregiver_party_id,
        'party_type', party_type_key_val,
        'user_email', user_email,
        'user_name', CONCAT(user_first_name, ' ', user_last_name),
        'first_name', user_first_name,
        'last_name', user_last_name
    );

EXCEPTION WHEN OTHERS THEN
    RETURN json_build_object(
        'eligible', 'no',
        'reason', 'Database error: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
