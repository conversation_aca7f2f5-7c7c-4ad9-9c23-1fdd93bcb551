# Game Assignment Email Setup

This document explains how to set up the game assignment email notification system for your RecallLoop project using Supabase webhooks.

## Overview

When a new game is assigned to a user, the system will:
1. Supabase webhook triggers the edge function
2. Edge function checks if the user's party type is 'caregiver' or 'tenant_admin'
3. If eligible, sends an email notification with game details
4. Uses the provided email template with user name, game name, and assignment date

## Components

### Supabase Edge Function (`supabase/functions/send_game_assignment_email/index.ts`)
- Receives webhook payload with `party_id`, `game_id`, `assigned_at`
- Queries the database to get user details and validate party type
- Sends email using SMTP
- Only sends emails to 'caregiver' or 'tenant_admin' party types

## Setup Instructions

### 1. Environment Variables

Add these to your `.env` file:

```env
# Database
DATABASE_URL=your_supabase_database_url

# SMTP Settings
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password

# Supabase (for edge function)
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

### 2. Set up Supabase Webhook

In your Supabase dashboard:
1. Go to Database > Webhooks
2. Create a new webhook for the `recall_loop.game_assignment` table
3. Set the webhook URL to your edge function: `https://your-project-ref.supabase.co/functions/v1/GameAssignmentEmail`
4. Configure it to trigger on `INSERT` events
5. Set the payload to include: `party_id`, `game_id`, `assigned_at`

### 3. Deploy Edge Function

```bash
# Deploy the edge function
supabase functions deploy GameAssignmentEmail

# Set environment variables for the edge function
supabase secrets set SMTP_HOST=your_smtp_host
supabase secrets set SMTP_PORT=587
supabase secrets set SMTP_USER=your_smtp_username
supabase secrets set SMTP_PASS=your_smtp_password
```

### 4. Test the Setup

The webhook will automatically trigger when you insert a new record into the `game_assignment` table.

## Database Schema Requirements

The system expects these tables in the `recall_loop` schema:

- `game_assignment` - with columns: `id`, `party_id`, `game_id`, `assigned_at`, `party_type_id`
- `party_type` - with columns: `id`, `party_type_key`
- `user_account` - with columns: `party_id`, `user_id`
- `person` - with columns: `party_id`, `first_name`, `last_name`
- `game` - with columns: `id`, `name`

## Testing

### Test Database Function
```bash
node test-webhook.js db
```

### Test Edge Function
```bash
node test-webhook.js edge
```

### Manual Test
Insert a test record into the game_assignment table:

```sql
INSERT INTO recall_loop.game_assignment (party_id, game_id, assigned_at, party_type_id)
VALUES ('your-party-id', 'your-game-id', NOW(), 'your-party-type-id');
```

## Email Template

The system uses this HTML email template:

```html
<p>Dear {{userName}},</p>

<p>Great news! You've been assigned a new game on the Competitor LDC Coaching Centre platform.</p>

<p>
  <strong>Game:</strong> {{gameName}}<br>
  <strong>Assigned On:</strong> {{assignedAt}}
</p>

<p>
  Please log in to your account to start playing and continue your progress.
</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
```

## Troubleshooting

### Check Logs
- Edge function logs: Supabase Dashboard > Functions > GameAssignmentEmail > Logs
- Webhook logs: Check console output from `node webhook.js`
- Database logs: Supabase Dashboard > Database > Logs

### Common Issues

1. **Email not sending**: Check SMTP credentials and settings
2. **Function not triggering**: Verify the trigger is created and active
3. **Database connection issues**: Check DATABASE_URL and permissions
4. **Schema access**: Ensure `recall_loop` schema is in the search path

### Debug Queries

Check if the trigger exists:
```sql
SELECT * FROM information_schema.triggers 
WHERE trigger_name = 'game_assignment_email_trigger_simple';
```

Test the email function directly:
```sql
SELECT recall_loop.send_game_assignment_email(
    'party-id'::uuid, 
    'game-id'::uuid, 
    NOW()
);
```

## Architecture Options

You have two main options for triggering emails:

### Option 1: Database Trigger + Webhook Listener (Current Setup)
- Automatic triggering on insert
- Uses PostgreSQL NOTIFY/LISTEN
- Webhook listener handles email sending

### Option 2: Direct Edge Function Call
- Call the edge function directly from your application
- More control over when emails are sent
- Requires application-level integration

Choose the option that best fits your application architecture.
