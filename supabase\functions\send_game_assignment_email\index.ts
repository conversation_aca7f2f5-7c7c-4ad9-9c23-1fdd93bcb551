import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from "./cors.ts";
import nodemailer from "npm:nodemailer@6.9.10";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

const transport = nodemailer.createTransport({
  host: Deno.env.get("SMTP_HOST"),
  port: Number(Deno.env.get("SMTP_PORT")),
  secure: true, // Use SSL
  auth: {
    user: Deno.env.get("SMTP_USER"),
    pass: Deno.env.get("SMTP_PASS"),
  },
});



Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method === "POST") {
    try {
      const requestData = await req.json();
      console.log('Request received:', requestData);

      // Extract data from request parameters
      const { party_id } = requestData;

      if (!party_id) {
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Missing required field: party_id"
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Call your RPC function to check party eligibility
      const { data: eligibilityResult, error: rpcError } = await supabase
        .rpc('check_party_eligibility', { party_id: party_id });

      if (rpcError) {
        console.error('RPC Error:', rpcError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Database error",
            error: rpcError.message
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      console.log('Eligibility check result:', eligibilityResult);

      // Check if the response is "no" - not eligible for email
      if (eligibilityResult.eligible === 'no') {
        console.log(`Party not eligible: ${eligibilityResult.message || 'Not eligible'}`);
        return new Response(
          JSON.stringify({
            status: "success",
            message: "Party not eligible for email notification",
            reason: eligibilityResult.message || 'Not eligible',
            party_type: eligibilityResult.party_type || null
          }),
          {
            status: 200,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // If we reach here, eligibilityResult.eligible === 'yes'
      // Get user details from auth.users using the party_id
      const { data: userAccount, error: userAccountError } = await supabase
        .from('recall_loop.user_account')
        .select('user_id')
        .eq('party_id', party_id)
        .single();

      if (userAccountError || !userAccount) {
        console.error('Error fetching user account:', userAccountError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "User account not found for party_id"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Get user details from auth.users
      const { data: authUser, error: authUserError } = await supabase.auth.admin
        .getUserById(userAccount.user_id);

      if (authUserError || !authUser.user) {
        console.error('Error fetching auth user:', authUserError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "User not found in auth"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Get user name from person table
      const { data: person, error: personError } = await supabase
        .from('recall_loop.person')
        .select('first_name, last_name')
        .eq('party_id', party_id)
        .single();

      if (personError || !person) {
        console.error('Error fetching person:', personError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Person details not found"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Prepare email data
      const userName = `${person.first_name} ${person.last_name}`.trim();
      const recipientEmail = authUser.user.email;
      const partyType = eligibilityResult.party_type;
      const gameName = eligibilityResult.game_name;
      const assignedOn = new Date(eligibilityResult.assigned_on).toLocaleDateString();

      // Game assignment email content
      const emailContent = `
<p>Dear ${userName},</p>

<p>Great news! You've been assigned a new game on the Competitor LDC Coaching Centre platform.</p>

<p>
  <strong>Game:</strong> ${gameName}<br>
  <strong>Assigned On:</strong> ${assignedOn}<br>
  <strong>Your Role:</strong> ${partyType}
</p>

<p>
  Please log in to your account to start playing and continue your progress.
</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
      `;

      // Send email
      await new Promise<void>((resolve, reject) => {
        transport.sendMail({
          from: Deno.env.get("SMTP_USER"),
          to: recipientEmail,
          subject: "New Game Assignment - Competitor LDC Coaching Centre",
          html: emailContent,
        }, (error: any) => {
          if (error) {
            return reject(error);
          }
          resolve();
        });
      });

      console.log(`Email sent successfully to ${recipientEmail} for game: ${gameName}`);

      return new Response(
        JSON.stringify({
          status: "success",
          message: "Game assignment email sent successfully",
          eligible: "yes",
          details: {
            recipient: recipientEmail,
            userName: userName,
            partyType: partyType,
            gameName: gameName,
            assignedOn: assignedOn,
            party_id: party_id
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );

    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response(
        JSON.stringify({
          status: "error",
          message: "Internal server error",
          error: error.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
  } else {
    return new Response("Method not allowed", { status: 405 });
  }
});
