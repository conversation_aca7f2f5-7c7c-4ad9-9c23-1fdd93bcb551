import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from "./cors.ts";
import nodemailer from "npm:nodemailer@6.9.10";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

const transport = nodemailer.createTransport({
  host: Deno.env.get("SMTP_HOST"),
  port: Number(Deno.env.get("SMTP_PORT")),
  secure: true, // Use SSL
  auth: {
    user: Deno.env.get("SMTP_USER"),
    pass: Deno.env.get("SMTP_PASS"),
  },
});



Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method === "POST") {
    try {
      const requestData = await req.json();
      console.log('Request received:', requestData);

      // Extract data from request parameters
      const { party_id } = requestData;

      if (!party_id) {
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Missing required field: party_id"
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Call the database function to check party eligibility
      const { data: eligibilityResult, error: rpcError } = await supabase
        .rpc('check_party_eligibility', { input_party_id: party_id });

      if (rpcError) {
        console.error('RPC Error:', rpcError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Database error",
            error: rpcError.message
          }),
          {
            status: 500,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      console.log('Eligibility check result:', eligibilityResult);

      // Check if the response is "no" - not eligible for email
      if (eligibilityResult.eligible === 'no') {
        console.log(`Party not eligible: ${eligibilityResult.reason}`);
        return new Response(
          JSON.stringify({
            status: "success",
            message: "Party not eligible for email notification",
            reason: eligibilityResult.reason,
            party_type: eligibilityResult.party_type || null
          }),
          {
            status: 200,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // If we reach here, eligibilityResult.eligible === 'yes'
      // Prepare email data from the function result
      const userName = eligibilityResult.user_name;
      const recipientEmail = eligibilityResult.user_email;
      const partyType = eligibilityResult.party_type;

      // Simple email content for party type notification
      const emailContent = `
<p>Dear ${userName},</p>

<p>This is a notification for your account.</p>

<p>Your party type: ${partyType}</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
      `;

      // Send email
      await new Promise<void>((resolve, reject) => {
        transport.sendMail({
          from: Deno.env.get("SMTP_USER"),
          to: recipientEmail,
          subject: "Account Notification - Competitor LDC Coaching Centre",
          html: emailContent,
        }, (error: any) => {
          if (error) {
            return reject(error);
          }
          resolve();
        });
      });

      console.log(`Email sent successfully to ${recipientEmail} for party type: ${partyType}`);

      return new Response(
        JSON.stringify({
          status: "success",
          message: "Email sent successfully",
          eligible: "yes",
          details: {
            recipient: recipientEmail,
            userName: userName,
            partyType: partyType,
            caregiver_party_id: eligibilityResult.caregiver_party_id
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );

    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response(
        JSON.stringify({
          status: "error",
          message: "Internal server error",
          error: error.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
  } else {
    return new Response("Method not allowed", { status: 405 });
  }
});
