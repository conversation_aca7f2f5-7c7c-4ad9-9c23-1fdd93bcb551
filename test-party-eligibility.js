// Test script to demonstrate the party eligibility check and email sending
const fetch = require('node-fetch');

// Your edge function URL
const EDGE_FUNCTION_URL = 'https://your-project-ref.supabase.co/functions/v1/send_game_assignment_email';

async function testPartyEligibility(partyId) {
    try {
        console.log(`\n🔍 Testing party eligibility for party_id: ${partyId}`);
        
        const response = await fetch(EDGE_FUNCTION_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer YOUR_ANON_KEY_HERE'
            },
            body: JSON.stringify({
                party_id: partyId
            })
        });

        const result = await response.json();
        
        console.log('📋 Response Status:', response.status);
        console.log('📋 Response Body:', JSON.stringify(result, null, 2));
        
        if (result.eligible === 'yes') {
            console.log('✅ ELIGIBLE: Email will be sent');
            console.log(`📧 Email will be sent to: ${result.details.recipient}`);
            console.log(`👤 User: ${result.details.userName}`);
            console.log(`🏷️ Party Type: ${result.details.partyType}`);
        } else if (result.eligible === 'no') {
            console.log('❌ NOT ELIGIBLE: No email will be sent');
            console.log(`📝 Reason: ${result.reason}`);
            if (result.party_type) {
                console.log(`🏷️ Party Type: ${result.party_type}`);
            }
        } else {
            console.log('⚠️ UNKNOWN RESPONSE');
        }
        
    } catch (error) {
        console.error('❌ Error testing party eligibility:', error.message);
    }
}

// Example usage
async function runTests() {
    console.log('🚀 Starting Party Eligibility Tests');
    
    // Test with different party IDs
    await testPartyEligibility('123e4567-e89b-12d3-a456-426614174000'); // Replace with actual party_id
    await testPartyEligibility('invalid-party-id');
    
    console.log('\n✅ Tests completed');
}

// Run the tests
runTests();

// You can also test individual party IDs like this:
// testPartyEligibility('your-actual-party-id-here');
