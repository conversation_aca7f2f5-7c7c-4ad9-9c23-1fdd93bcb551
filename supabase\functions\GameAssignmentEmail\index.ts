
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.3'
import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { corsHeaders } from "./cors.ts";
import nodemailer from "npm:nodemailer@6.9.10";

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

const transport = nodemailer.createTransport({
  host: Deno.env.get("SMTP_HOST"),
  port: Number(Deno.env.get("SMTP_PORT")),
  secure: true, // Use SSL
  auth: {
    user: Deno.env.get("SMTP_USER"),
    pass: Deno.env.get("SMTP_PASS"),
  },
});



Deno.serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  if (req.method === "POST") {
    try {
      const requestData = await req.json();
      console.log('Request received:', requestData);

      // Extract data from request parameters
      const { party_id } = requestData;

      if (!party_id) {
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Missing required field: party_id"
          }),
          {
            status: 400,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Step 1: Get caregiver_party_id from game_assignment table where id = party_id
      const { data: gameAssignment, error: gameAssignmentError } = await supabase
        .from('game_assignment')
        .select('caregiver_party_id')
        .eq('id', party_id)
        .single();

      if (gameAssignmentError || !gameAssignment) {
        console.error('Error fetching game assignment:', gameAssignmentError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Game assignment not found"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      const caregiver_party_id = gameAssignment.caregiver_party_id;

      // Step 2: Get party_type_id from party table where id = caregiver_party_id
      const { data: party, error: partyError } = await supabase
        .from('party')
        .select('party_type_id')
        .eq('id', caregiver_party_id)
        .single();

      if (partyError || !party) {
        console.error('Error fetching party:', partyError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Party not found"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Step 3: Get party_type_key from party_type table
      const { data: partyType, error: partyTypeError } = await supabase
        .from('party_type')
        .select('party_type_key')
        .eq('id', party.party_type_id)
        .single();

      if (partyTypeError || !partyType) {
        console.error('Error fetching party type:', partyTypeError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Party type not found"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Step 4: Check if party_type_key is 'caregiver' or 'tenant_admin'
      const allowedPartyTypes = ['caregiver', 'tenant_admin'];
      if (!allowedPartyTypes.includes(partyType.party_type_key.toLowerCase())) {
        console.log(`Party type ${partyType.party_type_key} not eligible for email notification`);
        return new Response(
          JSON.stringify({
            status: "success",
            message: "Party type not eligible for email notification"
          }),
          {
            status: 200,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Step 5: Get user email from auth.users using caregiver_party_id
      const { data: userAccount, error: userAccountError } = await supabase
        .from('user_account')
        .select('user_id')
        .eq('party_id', caregiver_party_id)
        .single();

      if (userAccountError || !userAccount) {
        console.error('Error fetching user account:', userAccountError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "User account not found"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Get user details from auth.users
      const { data: authUser, error: authUserError } = await supabase.auth.admin
        .getUserById(userAccount.user_id);

      if (authUserError || !authUser.user) {
        console.error('Error fetching auth user:', authUserError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "User not found in auth"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Step 6: Get user name from person table
      const { data: person, error: personError } = await supabase
        .from('person')
        .select('first_name, last_name')
        .eq('party_id', caregiver_party_id)
        .single();

      if (personError || !person) {
        console.error('Error fetching person:', personError);
        return new Response(
          JSON.stringify({
            status: "error",
            message: "Person details not found"
          }),
          {
            status: 404,
            headers: { ...corsHeaders, "Content-Type": "application/json" },
          }
        );
      }

      // Prepare email data
      const userName = `${person.first_name} ${person.last_name}`.trim();
      const recipientEmail = authUser.user.email;

      // Simple email content for party type notification
      const emailContent = `
<p>Dear ${userName},</p>

<p>This is a notification for your account.</p>

<p>Your party type: ${partyType.party_type_key}</p>

<br>
<p>Best regards,<br>
Competitor LDC Coaching Centre Team</p>
      `;

      // Send email
      await new Promise<void>((resolve, reject) => {
        transport.sendMail({
          from: Deno.env.get("SMTP_USER"),
          to: recipientEmail,
          subject: "Account Notification - Competitor LDC Coaching Centre",
          html: emailContent,
        }, (error: any) => {
          if (error) {
            return reject(error);
          }
          resolve();
        });
      });

      console.log(`Email sent successfully to ${recipientEmail} for party type: ${partyType.party_type_key}`);

      return new Response(
        JSON.stringify({
          status: "success",
          message: "Email sent successfully",
          details: {
            recipient: recipientEmail,
            userName: userName,
            partyType: partyType.party_type_key,
            caregiver_party_id: caregiver_party_id
          }
        }),
        {
          status: 200,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );

    } catch (error) {
      console.error("Error processing webhook:", error);
      return new Response(
        JSON.stringify({
          status: "error",
          message: "Internal server error",
          error: error.message
        }),
        {
          status: 500,
          headers: { ...corsHeaders, "Content-Type": "application/json" },
        }
      );
    }
  } else {
    return new Response("Method not allowed", { status: 405 });
  }
});